package com.example.mzsconnector.entity;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Supplier entity to verify proper construction and field mapping.
 */
@SpringBootTest
class SupplierTest {

    @Test
    void testSupplierBuilder() {
        // Given
        LocalDate validFromDate = LocalDate.of(2025, 8, 21);
        LocalDateTime now = LocalDateTime.now();
        
        // When
        Supplier supplier = Supplier.builder()
                .regNr("DE4076731467897")
                .umstId("DE257761637")
                .umstDrittId("DE257761637")
                .taxNr("*********")
                .umstidTaxnr("DE257761637")
                .kundenNr("35861")
                .kundenName("MOSAIK Software")
                .activeFlag("Y")
                .validFromDt(validFromDate)
                .validFlag(0)
                .relevantFlag(0)
                .createdAt(now)
                .updatedAt(now)
                .build();
        
        // Then
        assertNotNull(supplier);
        assertEquals("DE4076731467897", supplier.getRegNr());
        assertEquals("DE257761637", supplier.getUmstId());
        assertEquals("DE257761637", supplier.getUmstDrittId());
        assertEquals("*********", supplier.getTaxNr());
        assertEquals("DE257761637", supplier.getUmstidTaxnr());
        assertEquals("35861", supplier.getKundenNr());
        assertEquals("MOSAIK Software", supplier.getKundenName());
        assertEquals("Y", supplier.getActiveFlag());
        assertEquals(validFromDate, supplier.getValidFromDt());
        assertEquals(0, supplier.getValidFlag());
        assertEquals(0, supplier.getRelevantFlag());
        assertEquals(now, supplier.getCreatedAt());
        assertEquals(now, supplier.getUpdatedAt());
    }

    @Test
    void testSupplierNoArgsConstructor() {
        // When
        Supplier supplier = new Supplier();
        
        // Then
        assertNotNull(supplier);
        assertNull(supplier.getId());
        assertNull(supplier.getRegNr());
        assertNull(supplier.getUmstId());
    }

    @Test
    void testSupplierSettersAndGetters() {
        // Given
        Supplier supplier = new Supplier();
        LocalDate validFromDate = LocalDate.of(2025, 8, 21);
        
        // When
        supplier.setRegNr("DE4076731467897");
        supplier.setKundenNr("35861");
        supplier.setKundenName("MOSAIK Software");
        supplier.setActiveFlag("Y");
        supplier.setValidFromDt(validFromDate);
        supplier.setValidFlag(0);
        supplier.setRelevantFlag(0);
        supplier.setUmstidTaxnr("DE257761637");
        
        // Then
        assertEquals("DE4076731467897", supplier.getRegNr());
        assertEquals("35861", supplier.getKundenNr());
        assertEquals("MOSAIK Software", supplier.getKundenName());
        assertEquals("Y", supplier.getActiveFlag());
        assertEquals(validFromDate, supplier.getValidFromDt());
        assertEquals(0, supplier.getValidFlag());
        assertEquals(0, supplier.getRelevantFlag());
    }
}
