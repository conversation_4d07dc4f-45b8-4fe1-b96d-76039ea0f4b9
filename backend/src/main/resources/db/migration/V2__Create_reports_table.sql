-- Create report table for MZS quantity reporting
-- This table structure is hardcoded and should never be changed as per MZS requirements
-- Applies only to Germany and only to sales packaging
-- There can only be one report of each type for each year that is relevant

CREATE TABLE report (

    -- Quantity report ID - must be mapped in OneEPR as the reported ID
    -- Could be the same as in OneEPR for simplicity if format works
    -- Example: 1035023
    orderid BIGINT PRIMARY KEY,

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- User's first name
    -- Example: Jeremias
    userfirstname VARCHAR(255) NOT NULL,

    -- User's last name
    -- Example: Loew
    userlastname VARCHAR(255) NOT NULL,

    -- Company name from OneEPR
    -- Example: Loewenbau
    company VARCHAR(1020) NOT NULL,

    -- Contract VAT ID. If empty, contracttaxid must be set
    -- Example: DE350608869
    contractustid VARCHAR(120),

    -- Contract tax ID. If empty, contractustid must be set
    contracttaxid VARCHAR(120),

    -- Contract LUCID registration number
    -- Example: DE2060547948612
    contractregnr VARCHAR(120) NOT NULL,

    
    -- The quantity report number, needed for reference
    -- Example: 54988
    ordernumber BIGINT NOT NULL,
    
    -- Year of the quantity report
    -- Example: 2025
    ordercontractyear INTEGER NOT NULL,
    
    -- Year of first service association for packaging/Germany
    -- Example: 2025
    ordercontractstart INTEGER NOT NULL,
    
    -- Year of contract end - should be current year unless contract is extended
    -- Example: 2025
    ordercontractend INTEGER NOT NULL,
    
    -- When the report is entered into the reporting table (created_at equivalent)
    -- Example: 2024-11-30
    ordertime DATE NOT NULL,
    
    -- Type of report:
    -- JPM = Jahres-Prognosemeldung (Annual Forecast Report)
    -- UMM = Unterjährige Mengenmeldung (Interim Quantity Report)  
    -- JAM = Jahres-Abschlussmeldung (Annual Final Report)
    -- Example: UMM
    orderarticlename VARCHAR(255) NOT NULL CHECK (orderarticlename IN ('JPM', 'UMM', 'JAM')),
    
    -- Customer contract active flag
    -- 1 if customer contract active, 0 if inactive
    -- Example: 1
    useractive INTEGER NOT NULL CHECK (useractive IN (0, 1)),
    
    -- Status of the customer contract for the reporting year
    -- 0 = inactive, 1 = active, 2 = in termination, 3 = terminated
    -- Example: 1
    po_stat_id INTEGER NOT NULL CHECK (po_stat_id IN (0, 1, 2, 3)),
    
    -- Planned yearly quantities for glass in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionglas DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for paper/carton in kg, maximum 3 decimals
    -- Example: 10.000
    orderfractionppk DECIMAL(26,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for tinplate in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionwb DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for aluminium in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionalu DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for carton drink packaging in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractiongvk DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for other packagings in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionssv DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for plastics in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionkst DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Planned yearly quantities for natural materials in kg, maximum 3 decimals
    -- Example: 0.000
    orderfractionnat DECIMAL(22,3) NOT NULL DEFAULT 0.000,
    
    -- Net total of the licensing invoice for the report
    -- Not relevant for reporting, used for PowerBI report
    -- Example: 57.45
    invoiceamount DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    
    -- Indicates whether the report is relevant or not
    -- 0 = Yes (relevant), 8 = No (irrelevant)
    -- There can only be one report of each type for each year that is relevant
    -- Example: 0
    relevant_flag INTEGER NOT NULL CHECK (relevant_flag IN (0, 8))
);

-- Create indexes for performance
CREATE INDEX idx_report_orderid ON report(orderid);
CREATE INDEX idx_report_ordernumber ON report(ordernumber);
CREATE INDEX idx_report_ordercontractyear ON report(ordercontractyear);
CREATE INDEX idx_report_ordertime ON report(ordertime);
CREATE INDEX idx_report_orderarticlename ON report(orderarticlename);
CREATE INDEX idx_report_relevant_flag ON report(relevant_flag);
CREATE INDEX idx_report_useractive ON report(useractive);
CREATE INDEX idx_report_po_stat_id ON report(po_stat_id);
CREATE INDEX idx_report_created_at ON report(created_at);
CREATE INDEX idx_report_updated_at ON report(updated_at);

-- Composite indexes for common queries
CREATE INDEX idx_report_year_type_relevant ON report(ordercontractyear, orderarticlename, relevant_flag);
CREATE INDEX idx_report_year_relevant ON report(ordercontractyear, relevant_flag);


-- Either contractustid or contracttaxid must be provided
ALTER TABLE report ADD CONSTRAINT chk_report_contract_vat_or_tax
    CHECK (contractustid IS NOT NULL OR contracttaxid IS NOT NULL);

-- Contract LUCID number format validation
ALTER TABLE report ADD CONSTRAINT chk_report_contract_reg_nr_format
    CHECK (contractregnr ~ '^DE[0-9]{13}$');

-- Add constraints to ensure data integrity
-- Ensure contract years are logical
ALTER TABLE report ADD CONSTRAINT chk_report_contract_years 
    CHECK (ordercontractstart <= ordercontractyear AND ordercontractyear <= ordercontractend);

-- Ensure years are reasonable (not too far in past or future)
ALTER TABLE report ADD CONSTRAINT chk_report_year_range 
    CHECK (ordercontractyear >= 2020 AND ordercontractyear <= 2050);

-- Ensure all quantity fractions are non-negative
ALTER TABLE report ADD CONSTRAINT chk_report_quantities_non_negative 
    CHECK (
        orderfractionglas >= 0 AND
        orderfractionppk >= 0 AND
        orderfractionwb >= 0 AND
        orderfractionalu >= 0 AND
        orderfractiongvk >= 0 AND
        orderfractionssv >= 0 AND
        orderfractionkst >= 0 AND
        orderfractionnat >= 0
    );

-- Ensure invoice amount is non-negative
ALTER TABLE report ADD CONSTRAINT chk_report_invoice_amount_non_negative 
    CHECK (invoiceamount >= 0);

-- Add comments for documentation
COMMENT ON TABLE report IS 'Actual quantity report table for MZS reporting. Structure is hardcoded and should never be changed. Applies only to Germany and sales packaging.';
COMMENT ON COLUMN report.orderid IS 'Quantity report ID - must be mapped in OneEPR as reported ID';
COMMENT ON COLUMN report.ordernumber IS 'Quantity report number for reference';
COMMENT ON COLUMN report.ordercontractyear IS 'Year of the quantity report';
COMMENT ON COLUMN report.ordercontractstart IS 'Year of first service association for packaging/Germany';
COMMENT ON COLUMN report.ordercontractend IS 'Year of contract end';
COMMENT ON COLUMN report.ordertime IS 'When report is entered into reporting table';
COMMENT ON COLUMN report.orderarticlename IS 'Report type: JPM/UMM/JAM';
COMMENT ON COLUMN report.useractive IS 'Customer contract active flag (1=active, 0=inactive)';
COMMENT ON COLUMN report.po_stat_id IS 'Contract status (0=inactive, 1=active, 2=terminating, 3=terminated)';
COMMENT ON COLUMN report.orderfractionglas IS 'Planned yearly quantities for glass in kg';
COMMENT ON COLUMN report.orderfractionppk IS 'Planned yearly quantities for paper/carton in kg';
COMMENT ON COLUMN report.orderfractionwb IS 'Planned yearly quantities for tinplate in kg';
COMMENT ON COLUMN report.orderfractionalu IS 'Planned yearly quantities for aluminium in kg';
COMMENT ON COLUMN report.orderfractiongvk IS 'Planned yearly quantities for carton drink packaging in kg';
COMMENT ON COLUMN report.orderfractionssv IS 'Planned yearly quantities for other packagings in kg';
COMMENT ON COLUMN report.orderfractionkst IS 'Planned yearly quantities for plastics in kg';
COMMENT ON COLUMN report.orderfractionnat IS 'Planned yearly quantities for natural materials in kg';
COMMENT ON COLUMN report.invoiceamount IS 'Net total of licensing invoice (for PowerBI report)';
COMMENT ON COLUMN report.relevant_flag IS 'Report relevance (0=relevant, 8=irrelevant)';
COMMENT ON COLUMN report.userfirstname IS 'User first name';
COMMENT ON COLUMN report.userlastname IS 'User last name';
COMMENT ON COLUMN report.company IS 'Company name from OneEPR';
COMMENT ON COLUMN report.contractustid IS 'Contract VAT ID';
COMMENT ON COLUMN report.contracttaxid IS 'Contract tax ID';
COMMENT ON COLUMN report.contractregnr IS 'Contract LUCID registration number';